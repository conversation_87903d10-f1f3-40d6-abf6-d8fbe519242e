<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON><PERSON><PERSON> - AI & ML Blog</title>
  <!-- Bootstrap CSS -->
  <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet" />
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
  <!-- Your Main CSS -->
  <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}" />
  <!-- Enhanced Blog CSS -->
  <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-blog.css') }}" />
  <!-- Animate CSS -->
  <link rel="stylesheet" href="{{ url_for('static', filename='vendor/animate/animate.min.css') }}" />
</head>
<body>
  <!-- Enhanced Header / Navbar -->
  <header>
    <!-- Modern responsive navbar with enhanced mobile experience -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="transition: visibility 0.5s ease, opacity 0.5s ease;">
      <div class="container">
        <!-- Brand Logo -->
        <a class="navbar-brand" href="{{ url_for('home') }}">
          <span class="logo-text">Akshat Gupta</span>
        </a>

        <!-- Mobile Toggle Button -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
          <!-- Custom toggler icon will be added via JavaScript -->
        </button>

        <!-- Navbar Links -->
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item"><a class="nav-link" href="{{ url_for('home') }}"><i class="fas fa-home"></i> Home</a></li>
            <li class="nav-item"><a class="nav-link" href="{{ url_for('home') }}#about"><i class="fas fa-user"></i> About</a></li>
            <li class="nav-item"><a class="nav-link" href="{{ url_for('home') }}#academics"><i class="fas fa-graduation-cap"></i> Academics</a></li>
            <li class="nav-item"><a class="nav-link" href="{{ url_for('home') }}#skills"><i class="fas fa-code"></i> Skills</a></li>
            <li class="nav-item"><a class="nav-link" href="{{ url_for('home') }}#projects"><i class="fas fa-project-diagram"></i> Projects</a></li>
            <li class="nav-item"><a class="nav-link" href="{{ url_for('home') }}#certificates"><i class="fas fa-certificate"></i> Certificates</a></li>
            <li class="nav-item"><a class="nav-link" href="{{ url_for('home') }}#contact"><i class="fas fa-envelope"></i> Contact</a></li>
            <li class="nav-item active">
              <a class="nav-link" href="{{ url_for('blog') }}"><i class="fas fa-blog"></i> Blog</a>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  </header>

  <!-- Enhanced Blog Header -->
  <section class="blog-header">
    <!-- Neural Network Background -->
    <div class="blog-neural-bg">
      <canvas id="blogNetworkCanvas"></canvas>
    </div>

    <div class="container">
      <div class="blog-header-content wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
        <h1>AI & ML Insights</h1>
        <p>Exploring the depths of artificial intelligence, machine learning, and my journey building cutting-edge projects from scratch.</p>
      </div>
    </div>
  </section>

  <!-- Blog Categories -->
  <div class="container">
    <div class="blog-categories wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
      <div class="blog-category active" data-category="all">All Posts</div>
      <div class="blog-category" data-category="rag">RAG Systems</div>
      <div class="blog-category" data-category="generative">Generative AI</div>
      <div class="blog-category" data-category="nlp">NLP & LLMs</div>
      <div class="blog-category" data-category="cv">Computer Vision</div>
      <div class="blog-category" data-category="tutorial">Tutorials</div>
    </div>
  </div>

  <!-- Blog Posts Section -->
  <section class="blog-posts">
    <div class="container">
      <div class="row">
        <div class="col-lg-8 offset-lg-2">
          <!-- Coming Soon Message -->
          <div class="blog-post wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
            <div class="text-center py-5">
              <i class="fas fa-pen-fancy fa-4x mb-4" style="color: #b42c1d;"></i>
              <h2>Blog Content Coming Soon</h2>
              <p class="mt-4">
                I'm currently working on creating valuable content related to my AI and ML projects.
                Check back soon for articles about:
              </p>
              <div class="mt-4 d-flex flex-wrap justify-content-center gap-3">
                <span class="badge">Medical ChatBot</span>
                <span class="badge">Computer Vision</span>
                <span class="badge">Transformer Models</span>
                <span class="badge">RAG Systems</span>
                <span class="badge">Generative AI</span>
              </div>
              <p class="mt-4">
                In the meantime, feel free to explore my <a href="{{ url_for('home') }}#projects" class="text-decoration-none" style="color: #b42c1d;">projects</a>
                or connect with me on <a href="https://github.com/Akshat-Gupta04" target="_blank" class="text-decoration-none" style="color: #b42c1d;">GitHub</a>.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer>
    <div class="container text-center">
      <p>&copy; 2024 Akshat Gupta. All Rights Reserved.</p>
    </div>
  </footer>

  <!-- Bootstrap JS -->
  <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
  <!-- WOW JS -->
  <script src="{{ url_for('static', filename='vendor/wow/wow.min.js') }}"></script>
  <!-- Blog Network Animation -->
  <script src="{{ url_for('static', filename='js/blog-network.js') }}"></script>

  <script>
    // Initialize WOW.js
    new WOW().init();

    // Category filtering
    document.querySelectorAll('.blog-category').forEach(function(button) {
      button.addEventListener('click', function() {
        // Remove active class from all buttons
        document.querySelectorAll('.blog-category').forEach(function(btn) {
          btn.classList.remove('active');
        });

        // Add active class to clicked button
        this.classList.add('active');
      });
    });
  </script>
</body>
</html>
