<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON><PERSON><PERSON> - AI Portfolio</title>
  <meta name="description" content="<PERSON><PERSON><PERSON> Gupta AI Portfolio" />
  <meta name="author" content="<PERSON>ks<PERSON>" />

  <!-- Preconnect to Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

  <!-- Google Fonts -->

  <!-- Bootstrap CSS -->
  <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet" />

  <!-- Additional CSS Files -->
  <link rel="stylesheet" href="{{ url_for('static', filename='css/fontawesome.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/templatemo-digimedia-v1.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/animated.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/animate.min.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/owl.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/modern-contact.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-navbar.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/advanced-skills.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-academics.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/ai-projects.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/modern-certificates.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-certificates.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/futuristic-chatbot.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-about.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-banner.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/ai-decorative.css') }}" />
  <link rel="stylesheet" href="{{ url_for('static', filename='css/ai-splash.css') }}" />

  <!-- Formal Splash Animation Script -->
  <script src="{{ url_for('static', filename='js/formal-splash.js') }}" defer></script>

  <!-- Banner Network Animation Script -->
  <script src="{{ url_for('static', filename='js/banner-network.js') }}" defer></script>

  <!-- Font Awesome for Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

  <!-- Custom Inline Styles for SPLASH ANIMATION, Notification Banner, and Responsive Navbar -->
  <style>
    :root {
      --circle-img-size: calc(min(3vw + 70px, 140px));
    }

    @media (max-height: 800px) {
      :root {
        --circle-img-size: calc(min(2.5vw + 50px, 110px));
      }
    }

    @media (max-height: 700px) {
      :root {
        --circle-img-size: calc(min(2vw + 40px, 90px));
      }
    }

    @media (max-height: 600px) {
      :root {
        --circle-img-size: calc(min(1.5vw + 30px, 70px));
      }
    }

    /* Floating Chat Button remains unchanged */
    .floating-chat-btn {
      position: fixed;
      bottom: 60px;
      right: 30px;
      z-index: 1060;
    }
    .notification-banner {
      width: 100%;
      background: linear-gradient(90deg, #1f1f1f, #3c3c3c);
      padding: 10px 0;
      overflow: hidden;
      white-space: nowrap;
      border-bottom: 2px solid #444;
      /* Positioned in normal document flow so it appears only above the main banner */
    }
    .notification-text {
  display: inline-block;
  color: #fff;  /* Plain white text */
  font-size: 1.2rem;
  font-family: 'Poppins', sans-serif;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-weight: 500;
  /* Removed gradient and text-fill properties for a normal font style */
  text-shadow: 1px 1px 2px rgba(0,0,0,0.6); /* Retain subtle shadow for legibility */
  animation: marquee 15s linear infinite;
}

@keyframes marquee {
  0%   { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

    /* --------------------------------------------------------------------
       PREMIUM SPLASH ANIMATION OVERLAY
       An immersive, high-end splash screen with 3D effects, dynamic animations, and interactive elements
    --------------------------------------------------------------------- */
    #splash {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
      background: #000000;
      z-index: 9998; /* Reduced z-index to ensure navbar stays on top */
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      perspective: 1000px; /* Enable 3D perspective */
    }

    /* 3D Particle Background */
    .splash-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    #particleCanvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      animation: fadeIn 2s ease-in-out forwards;
    }

    /* Glowing Orbs */
    .glow-orbs {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
    }

    .orb {
      position: absolute;
      border-radius: 50%;
      filter: blur(60px);
      opacity: 0;
      animation: orbFloat 15s ease-in-out infinite;
    }

    .orb-1 {
      width: 40vw;
      height: 40vw;
      background: radial-gradient(circle, rgba(180, 44, 29, 0.15) 0%, rgba(0, 0, 0, 0) 70%);
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }

    .orb-2 {
      width: 30vw;
      height: 30vw;
      background: radial-gradient(circle, rgba(255, 126, 95, 0.1) 0%, rgba(0, 0, 0, 0) 70%);
      bottom: 10%;
      right: 20%;
      animation-delay: 5s;
    }

    .orb-3 {
      width: 25vw;
      height: 25vw;
      background: radial-gradient(circle, rgba(255, 200, 150, 0.08) 0%, rgba(0, 0, 0, 0) 70%);
      top: 40%;
      right: 30%;
      animation-delay: 10s;
    }

    @keyframes orbFloat {
      0% { transform: translate(0, 0); opacity: 0; }
      25% { opacity: 1; }
      50% { transform: translate(-10%, -5%); opacity: 0.8; }
      75% { opacity: 0.5; }
      100% { transform: translate(0, 0); opacity: 0; }
    }

    /* Main Content Container */
    .splash-content {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 2;
      width: 100%;
      max-width: 1200px;
      padding: 0 20px;
      transform-style: preserve-3d; /* Enable 3D for children */
      animation: contentFloat 6s ease-in-out infinite;
      height: 100vh;
      overflow-y: auto;
      /* Ensure content is always visible by adding padding */
      padding-top: max(20px, 5vh);
      padding-bottom: max(20px, 5vh);
    }

    @keyframes contentFloat {
      0% { transform: translateY(0) rotateX(0deg); }
      50% { transform: translateY(-10px) rotateX(1deg); }
      100% { transform: translateY(0) rotateX(0deg); }
    }

    /* Simplified Logo */
    .premium-logo {
      position: relative;
      margin-bottom: 40px;
      transform-style: preserve-3d;
    }

    .logo-container {
      position: relative;
      width: 120px;
      height: 120px;
      margin: 0 auto;
      opacity: 0;
      transform: scale(0.8);
      animation: logoReveal 1.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards 0.5s;
    }

    .logo-circle {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #b42c1d, #ff7e5f);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 10px 30px rgba(180, 44, 29, 0.4);
    }

    .logo-text {
      color: #ffffff;
      font-size: 3rem;
      font-weight: 800;
      letter-spacing: -1px;
      text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    }

    .logo-ring {
      position: absolute;
      top: -10px;
      left: -10px;
      right: -10px;
      bottom: -10px;
      border: 3px solid rgba(180, 44, 29, 0.5);
      border-radius: 50%;
      animation: logoRing 10s linear infinite;
    }

    @keyframes logoReveal {
      0% { opacity: 0; transform: scale(0.8) translateY(20px); }
      100% { opacity: 1; transform: scale(1) translateY(0); }
    }

    @keyframes logoRing {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Developer-Style Title */
    .premium-title {
      position: relative;
      margin-bottom: 60px;
      text-align: left;
      transform-style: preserve-3d;
      max-width: 600px;
      width: 100%;
    }

    .code-title {
      background: rgba(30, 30, 30, 0.7);
      border-radius: 8px;
      padding: 20px;
      font-family: 'Consolas', 'Monaco', monospace;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      border-left: 4px solid #b42c1d;
      opacity: 0;
      transform: translateY(20px);
      animation: codeReveal 1s cubic-bezier(0.16, 1, 0.3, 1) forwards 0.8s;
    }

    .code-comment {
      color: #6A9955;
      display: block;
      margin-bottom: 10px;
      font-size: 1rem;
      opacity: 0;
      animation: typingEffect 0.8s steps(20) forwards 1.2s;
      white-space: nowrap;
      overflow: hidden;
      width: 0;
    }

    .code-heading {
      color: #DCDCDC;
      font-size: 1.5rem;
      font-weight: normal;
      margin: 0 0 10px 0;
      line-height: 1.5;
    }

    .code-keyword {
      color: #569CD6;
      animation: colorPulse 3s infinite;
    }

    .code-variable {
      color: #4FC1FF;
    }

    .code-operator {
      color: #D4D4D4;
    }

    .code-function {
      color: #DCDCDC;
    }

    .code-bracket {
      color: #D4D4D4;
    }

    .code-content {
      margin-left: 20px;
      margin-top: 5px;
      margin-bottom: 5px;
      position: relative;
      overflow: hidden;
    }

    .code-return {
      color: #DCDCDC;
      display: block;
      position: relative;
      opacity: 0;
      animation: slideIn 0.5s ease forwards 2s;
    }

    .code-string {
      color: #CE9178;
      position: relative;
    }

    .code-string::after {
      content: '';
      position: absolute;
      right: -12px;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 16px;
      background: #CE9178;
      animation: cursorBlink 1s step-end infinite;
    }

    @keyframes codeReveal {
      0% { opacity: 0; transform: translateY(20px); }
      100% { opacity: 1; transform: translateY(0); }
    }

    @keyframes typingEffect {
      from { width: 0; }
      to { width: 100%; opacity: 1; }
    }

    @keyframes slideIn {
      0% { opacity: 0; transform: translateX(-20px); }
      100% { opacity: 1; transform: translateX(0); }
    }

    @keyframes cursorBlink {
      0%, 100% { opacity: 1; }
      50% { opacity: 0; }
    }

    @keyframes colorPulse {
      0%, 100% { color: #569CD6; }
      50% { color: #4FC1FF; }
    }

    .title-bar {
      width: 0;
      height: 4px;
      background: linear-gradient(90deg, #b42c1d, #ff7e5f);
      margin: 15px auto 20px;
      animation: barGrow 1.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards 1.2s;
      position: relative;
      overflow: hidden;
    }

    .title-bar::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 30px;
      height: 100%;
      background: rgba(255, 255, 255, 0.3);
      filter: blur(5px);
      animation: barShine 3s ease-in-out infinite 2s;
    }

    @keyframes barGrow {
      to { width: 180px; }
    }

    @keyframes barShine {
      0% { transform: translateX(-30px); }
      100% { transform: translateX(180px); }
    }

    .subtitle {
      color: rgba(255, 255, 255, 0.7);
      font-size: 1.1rem;
      font-family: 'Consolas', 'Monaco', monospace;
      margin-top: 15px;
      opacity: 0;
      animation: fadeIn 1s ease forwards 2.5s;
      display: flex;
      align-items: center;
    }

    .subtitle::before {
      content: '>';
      color: #b42c1d;
      margin-right: 8px;
      font-weight: bold;
      animation: cursorBlink 1s step-end infinite;
    }

    /* Premium Gallery */
    .premium-gallery {
      width: 100%;
      max-width: min(900px, 95vw);
      margin-bottom: clamp(20px, 5vh, 50px);
      perspective: 1000px;
    }

    .gallery-container {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      gap: clamp(10px, 2vw, 20px);
      transform-style: preserve-3d;
      opacity: 0;
      animation: fadeIn 1s ease forwards 1.8s;
      max-width: 90vw;
      margin: 0 auto;
    }

    .gallery-item {
      width: var(--circle-img-size);
      height: var(--circle-img-size);
      position: relative;
      transform-style: preserve-3d;
      transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      cursor: pointer;
      animation: formalFloat 4s ease-in-out infinite;
    }

    /* Formal, structured floating animation */
    @keyframes formalFloat {
      0% {
        transform: translateY(0);
      }
      50% {
        transform: translateY(-10px);
      }
      100% {
        transform: translateY(0);
      }
    }

    /* Add wrapper for better 3D effects */
    .gallery-item-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
      transform-style: preserve-3d;
      transition: transform 0.4s ease;
    }

    /* Add reflection effect */
    .gallery-reflection {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 60%);
      border-radius: 10px;
      opacity: 0;
      transition: opacity 0.4s ease;
      pointer-events: none;
    }

    .gallery-item img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      border: 2px solid transparent;
      backface-visibility: hidden;
    }

    .gallery-item:hover .gallery-item-wrapper {
      transform: translateZ(15px);
    }

    .gallery-item:hover img {
      border-color: rgba(180, 44, 29, 0.5);
      box-shadow: 0 15px 40px rgba(180, 44, 29, 0.3);
    }

    .gallery-item:hover .gallery-reflection {
      opacity: 0.2;
    }

    .gallery-item.active .gallery-item-wrapper {
      transform: translateZ(30px);
      animation: none; /* Stop floating animation when active */
    }

    .gallery-item.active img {
      border-color: #b42c1d;
      box-shadow: 0 20px 50px rgba(180, 44, 29, 0.5);
    }

    .gallery-controls {
      margin-top: 30px;
      text-align: center;
      opacity: 0;
      animation: fadeIn 1s ease forwards 2s;
    }

    .gallery-instruction {
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(255, 255, 255, 0.7);
      font-size: 1rem;
      letter-spacing: 1px;
    }

    .pulse-dot {
      width: 10px;
      height: 10px;
      background: #b42c1d;
      border-radius: 50%;
      margin-right: 10px;
      animation: pulseDot 2s infinite;
    }

    @keyframes pulseDot {
      0% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.5); opacity: 0.5; }
      100% { transform: scale(1); opacity: 1; }
    }

    /* Premium Loader */
    .premium-loader {
      display: flex;
      align-items: center;
      margin-top: 20px;
      opacity: 0;
      animation: fadeIn 1s ease forwards 2.2s;
    }

    .loader-track {
      width: 250px;
      height: 4px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
      overflow: hidden;
      position: relative;
    }

    .loader-fill {
      width: 0%;
      height: 100%;
      background: linear-gradient(90deg, #b42c1d, #ff7e5f);
      transition: width 0.3s ease;
      position: relative;
    }

    .loader-fill::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 30px;
      height: 100%;
      background: rgba(255, 255, 255, 0.3);
      filter: blur(3px);
      animation: loaderShine 2s ease-in-out infinite;
    }

    @keyframes loaderShine {
      0% { transform: translateX(-30px); }
      100% { transform: translateX(250px); }
    }

    .loader-percentage {
      margin-left: 15px;
      color: #ffffff;
      font-size: 1rem;
      font-weight: 600;
      min-width: 40px;
    }

    @keyframes fadeIn {
      0% { opacity: 0; }
      100% { opacity: 1; }
    }

    /* --------------------------------------------------------------------
       PROFILE IMAGE STYLES for Main Banner & About Section
       (Fixed dimensions without rounding)
    --------------------------------------------------------------------- */
    #mainProfilePic,
    #aboutProfilePic {
      width: 100%;
      height: 360;
      border-radius: 10px;
      object-fit: cover;
      border: none;
      box-shadow: none;
      display: block;
      margin: 0 auto;
    }

    /* --------------------------------------------------------------------
       (Optional) Notification Banner - if you want one at the top of your content
    --------------------------------------------------------------------- */
    .notification-banner {
      width: 100%;
      background: linear-gradient(90deg, #1f1f1f, #3c3c3c);
      padding: 10px 0;
      overflow: hidden;
      white-space: nowrap;
      border-bottom: 2px solid #444;
    }
    .notification-text {
  display: inline-block;
  font-size: 1.2rem;
  font-family: 'Poppins', sans-serif;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-weight: 600;
  background: linear-gradient(45deg, #ffffff, #dddddd); /* Brighter gradient for higher contrast */
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8); /* Increased shadow for better legibility */
  animation: marquee 15s linear infinite;
}

@keyframes marquee {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}
  </style>
</head>
<body>
  <!-- (Optional) Notification Banner (if you want it visible only above the main banner) -->
  <!-- Uncomment the block below if needed -->
  <!--
  <div class="notification-banner">
    <div class="notification-text">
      None of image is real, all are generated using Custom LORA model &bull;
    </div>
  </div>
  -->

  <!-- PREMIUM SPLASH ANIMATION OVERLAY -->
  <div id="splash">
    <!-- 3D Particle Background -->
    <div class="splash-background">
      <canvas id="particleCanvas"></canvas>
      <div class="glow-orbs">
        <div class="orb orb-1"></div>
        <div class="orb orb-2"></div>
        <div class="orb orb-3"></div>
      </div>

      <!-- AI/ML Specific Elements -->
      <!-- AI Brain Outline -->
      <div class="splash-brain-outline"></div>

      <!-- Neural Network Nodes -->
      <div class="neural-node" style="top: 20%; left: 30%;"></div>
      <div class="neural-node" style="top: 25%; left: 40%;"></div>
      <div class="neural-node" style="top: 30%; left: 50%;"></div>
      <div class="neural-node" style="top: 35%; left: 60%;"></div>
      <div class="neural-node" style="top: 40%; left: 70%;"></div>
      <div class="neural-node" style="top: 60%; left: 25%;"></div>
      <div class="neural-node" style="top: 65%; left: 35%;"></div>
      <div class="neural-node" style="top: 70%; left: 45%;"></div>
      <div class="neural-node" style="top: 75%; left: 55%;"></div>
      <div class="neural-node" style="top: 80%; left: 65%;"></div>

      <!-- Neural Network Connections -->
      <div class="neural-connection" style="top: 22%; left: 30%; width: 100px; transform: rotate(15deg);"></div>
      <div class="neural-connection" style="top: 27%; left: 40%; width: 100px; transform: rotate(15deg);"></div>
      <div class="neural-connection" style="top: 32%; left: 50%; width: 100px; transform: rotate(15deg);"></div>
      <div class="neural-connection" style="top: 62%; left: 25%; width: 100px; transform: rotate(15deg);"></div>
      <div class="neural-connection" style="top: 67%; left: 35%; width: 100px; transform: rotate(15deg);"></div>
      <div class="neural-connection" style="top: 72%; left: 45%; width: 100px; transform: rotate(15deg);"></div>

      <!-- AI/ML Icons -->
      <img src="https://cdn-icons-png.flaticon.com/512/2103/2103633.png" alt="AI Brain" class="ai-ml-icon" style="top: 15%; left: 15%; width: 50px; animation-delay: 0s;">
      <img src="https://cdn-icons-png.flaticon.com/512/2103/2103658.png" alt="Machine Learning" class="ai-ml-icon" style="top: 25%; right: 15%; width: 50px; animation-delay: 1s;">
      <img src="https://cdn-icons-png.flaticon.com/512/8637/8637099.png" alt="Neural Network" class="ai-ml-icon" style="bottom: 20%; left: 20%; width: 50px; animation-delay: 2s;">
      <img src="https://cdn-icons-png.flaticon.com/512/6295/6295417.png" alt="Deep Learning" class="ai-ml-icon" style="bottom: 30%; right: 20%; width: 50px; animation-delay: 3s;">

      <!-- Binary Code -->
      <div class="splash-binary" style="top: 20%; left: 10%; animation-delay: 0s;">01000001 01001001</div>
      <div class="splash-binary" style="top: 40%; right: 10%; animation-delay: 2s;">01001101 01001100</div>
      <div class="splash-binary" style="bottom: 30%; left: 15%; animation-delay: 4s;">01000100 01000101 01000101 01010000</div>

      <!-- Data Flow Lines -->
      <div class="splash-flow-line" style="width: 20%; top: 35%; left: 10%; transform: rotate(15deg); animation-delay: 0s;"></div>
      <div class="splash-flow-line" style="width: 25%; top: 55%; right: 15%; transform: rotate(-15deg); animation-delay: 1.5s;"></div>
      <div class="splash-flow-line" style="width: 30%; bottom: 25%; left: 20%; transform: rotate(20deg); animation-delay: 3s;"></div>

      <!-- AI Code Snippets -->
      <div class="splash-code" style="top: 15%; right: 10%; animation-delay: 1s;">
        <div><span class="splash-keyword">import</span> tensorflow <span class="splash-keyword">as</span> tf</div>
        <div><span class="splash-comment"># Neural network</span></div>
        <div>model = tf.keras.Sequential()</div>
      </div>
      <div class="splash-code" style="bottom: 15%; left: 10%; animation-delay: 5s;">
        <div><span class="splash-keyword">def</span> <span class="splash-function">train</span>(X, y):</div>
        <div>&nbsp;&nbsp;<span class="splash-keyword">return</span> model.fit(X, y)</div>
      </div>

      <!-- AI/ML Terminology -->
      <div class="ai-term" style="top: 30%; left: 40%; animation-delay: 0s;">Deep Learning</div>
      <div class="ai-term" style="top: 50%; right: 30%; animation-delay: 2s;">Neural Networks</div>
      <div class="ai-term" style="bottom: 40%; left: 35%; animation-delay: 4s;">Computer Vision</div>
      <div class="ai-term" style="bottom: 20%; right: 40%; animation-delay: 6s;">NLP</div>

      <!-- Enhanced Particles -->
      <div class="enhanced-particle" style="top: 25%; left: 25%; width: 30px; height: 30px; animation-delay: 0s;"></div>
      <div class="enhanced-particle" style="top: 35%; right: 30%; width: 20px; height: 20px; animation-delay: 1s;"></div>
      <div class="enhanced-particle" style="bottom: 30%; left: 40%; width: 25px; height: 25px; animation-delay: 2s;"></div>
    </div>

    <!-- Main Content -->
    <div class="splash-content">
      <!-- Simplified Logo -->
      <div class="premium-logo">
        <div class="logo-container">
          <div class="logo-circle">
            <span class="logo-text">AG</span>
          </div>
          <div class="logo-ring"></div>
        </div>
      </div>

      <!-- Developer-Style Animated Title -->
      <div class="premium-title">
        <div class="code-title">
          <span class="code-comment">// Welcome to my</span>
          <h1 class="code-heading"><span class="code-keyword">const</span> <span class="code-variable">portfolio</span><span class="code-function">() </span> <span class="code-bracket">{</span></h1>
          <div class="code-content">
            <span class="code-return">return <span class="code-string">"Akshat Gupta"</span>;</span>
          </div>
          <span class="code-bracket">}</span>
        </div>
        <div class="title-bar"></div>
        <p class="subtitle">AI/ML Enthusiast</p>
      </div>

      <!-- 3D Image Carousel -->
      <div class="premium-gallery">
        <div class="gallery-container" id="galleryContainer">
          <!-- Images will be dynamically added here -->
        </div>
        <div class="gallery-controls">
          <div class="gallery-instruction">
            <span class="pulse-dot"></span>
            <span>Randomly selecting images for your profile</span>
          </div>
        </div>
      </div>

      <!-- Interactive Loading Progress -->
      <div class="premium-loader">
        <div class="loader-track">
          <div class="loader-fill" id="loaderFill"></div>
        </div>
        <div class="loader-percentage" id="loaderPercentage">0%</div>
      </div>
    </div>

    <!-- Hidden Image Container -->
    <div id="imageContainer" style="display: none;">
      <!-- Hidden container for preloading images -->
    </div>
  </div>

  <!-- ***** Enhanced Header / Navbar ***** -->
  <header>
    <!-- Modern responsive navbar with enhanced mobile experience -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="transition: visibility 0.5s ease, opacity 0.5s ease;">
      <div class="container">
        <!-- Brand Logo -->
        <a class="navbar-brand" href="#top">
          <span class="logo-text">Akshat Gupta</span>
        </a>

        <!-- Mobile Toggle Button -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
          <!-- Custom toggler icon will be added via JavaScript -->
        </button>

        <!-- Navbar Links -->
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item"><a class="nav-link" href="#top">Home</a></li>
            <li class="nav-item"><a class="nav-link" href="#about">About</a></li>
            <li class="nav-item"><a class="nav-link" href="#academics">Acadamics</a></li>
            <li class="nav-item"><a class="nav-link" href="#skills">Skills</a></li>
            <li class="nav-item"><a class="nav-link" href="#projects">Projects</a></li>
            <li class="nav-item"><a class="nav-link" href="#certificates">Certificates</a></li>
            <li class="nav-item"><a class="nav-link" href="#contact">Contact</a></li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('blog') }}">Blog</a>
            </li>
            <li class="nav-item">
              <a class="nav-link download-resume" href="{{ url_for('static', filename='Resume_AkshatGupta.pdf') }}" download>
                Download Resume
              </a>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  </header>
  <!-- <div class="notification-banner">
    <div class="notification-text">
      None of image is real, all are generated using Custom LORA model &bull; Use the Floating AI chatbot to get more information about me and my projects&bull;
    </div> -->
  </div>


  <!-- ***** Enhanced Main Banner Area ***** -->
  <div id="top" class="main-banner">
    <!-- Neural Network Background -->
    <div class="neural-banner-bg">
      <canvas id="bannerNetworkCanvas"></canvas>
    </div>

    <!-- Decorative Elements -->
    <div class="decorative-circle circle-1"></div>
    <div class="decorative-circle circle-2"></div>
    <div class="decorative-circle circle-3"></div>

    <!-- AI/ML Specific Decorative Elements -->
    <div class="ai-brain-outline"></div>

    <!-- Binary Code Lines -->
    <div class="binary-code binary-line-1">01001001 00100000 01100001 01101101 00100000 01100001 01101110 00100000 01000001 01001001 00100000 01100001 01101110 01100100 00100000 01001101 01001100 00100000 01000100 01100101 01110110 01100101 01101100 01101111 01110000 01100101 01110010</div>
    <div class="binary-code binary-line-2">01000001 01110010 01110100 01101001 01100110 01101001 01100011 01101001 01100001 01101100 00100000 01001001 01101110 01110100 01100101 01101100 01101100 01101001 01100111 01100101 01101110 01100011 01100101</div>

    <!-- Neural Network Layers -->
    <div class="neural-layer layer-1">
      <div class="neuron" style="top: 10%; left: 50%;"></div>
      <div class="neuron" style="top: 25%; left: 50%;"></div>
      <div class="neuron" style="top: 40%; left: 50%;"></div>
      <div class="neuron" style="top: 55%; left: 50%;"></div>
      <div class="neuron" style="top: 70%; left: 50%;"></div>
      <div class="neuron" style="top: 85%; left: 50%;"></div>
    </div>
    <div class="neural-layer layer-2">
      <div class="neuron" style="top: 10%; left: 50%;"></div>
      <div class="neuron" style="top: 25%; left: 50%;"></div>
      <div class="neuron" style="top: 40%; left: 50%;"></div>
      <div class="neuron" style="top: 55%; left: 50%;"></div>
      <div class="neuron" style="top: 70%; left: 50%;"></div>
      <div class="neuron" style="top: 85%; left: 50%;"></div>
    </div>

    <!-- Data Flow Lines -->
    <div class="data-flow">
      <div class="flow-line flow-line-1"></div>
      <div class="flow-line flow-line-2"></div>
      <div class="flow-line flow-line-3"></div>
      <div class="flow-line flow-line-4"></div>
    </div>

    <!-- AI Code Snippets -->
    <div class="code-snippet code-snippet-1">
      <div><span class="keyword">import</span> tensorflow <span class="keyword">as</span> tf</div>
      <div><span class="keyword">from</span> tensorflow.keras <span class="keyword">import</span> layers</div>
      <div><span class="comment"># Neural network model</span></div>
      <div>model = tf.keras.Sequential([</div>
      <div>&nbsp;&nbsp;layers.Dense(128, activation=<span class="function">'relu'</span>),</div>
      <div>&nbsp;&nbsp;layers.Dense(64, activation=<span class="function">'relu'</span>),</div>
      <div>&nbsp;&nbsp;layers.Dense(10, activation=<span class="function">'softmax'</span>)</div>
      <div>])</div>
    </div>
    <div class="code-snippet code-snippet-2">
      <div><span class="keyword">def</span> <span class="function">train_model</span>(X, y):</div>
      <div>&nbsp;&nbsp;model.compile(</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;optimizer=<span class="function">'adam'</span>,</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;loss=<span class="function">'categorical_crossentropy'</span>,</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;metrics=[<span class="function">'accuracy'</span>]</div>
      <div>&nbsp;&nbsp;)</div>
      <div>&nbsp;&nbsp;<span class="keyword">return</span> model.fit(X, y, epochs=10)</div>
    </div>

    <!-- Floating AI/ML Tech Icons -->
    <div class="floating-tech-icons">
      <img src="https://cdn-icons-png.flaticon.com/512/2103/2103633.png" alt="AI Brain" class="tech-icon tech-icon-1" width="70">
      <img src="https://cdn-icons-png.flaticon.com/512/2103/2103658.png" alt="Machine Learning" class="tech-icon tech-icon-2" width="70">
      <img src="https://cdn-icons-png.flaticon.com/512/8637/8637099.png" alt="Neural Network" class="tech-icon tech-icon-3" width="70">
      <img src="https://cdn-icons-png.flaticon.com/512/6295/6295417.png" alt="Deep Learning" class="tech-icon tech-icon-4" width="70">
      <img src="https://cdn-icons-png.flaticon.com/512/2103/2103832.png" alt="Data Science" class="tech-icon tech-icon-5" width="70">
      <img src="https://cdn-icons-png.flaticon.com/512/2103/2103787.png" alt="Computer Vision" class="tech-icon tech-icon-6" width="70">
      <img src="https://cdn-icons-png.flaticon.com/512/9589/9589274.png" alt="NLP" class="tech-icon tech-icon-7" width="70" style="top: 60%; right: 20%; animation: float-icon 9s ease-in-out infinite 1.5s;">
      <img src="https://cdn-icons-png.flaticon.com/512/2103/2103665.png" alt="Robotics" class="tech-icon tech-icon-8" width="70" style="bottom: 15%; left: 15%; animation: float-icon 11s ease-in-out infinite 3.5s;">
    </div>

    <div class="container">
      <div class="row align-items-center">
        <!-- Text Section (Left Side) -->
        <div class="col-lg-6">
          <h6 class="wow fadeInLeft" data-wow-duration="1s" data-wow-delay="0.3s"
              style="text-transform: uppercase; color: #b42c1d; font-size: 22px; font-weight: 700; letter-spacing: 2px;">
            Welcome to My Portfolio
          </h6>
          <h2 class="banner-title wow fadeInLeft" data-wow-duration="1s" data-wow-delay="0.5s">
            <span>I'm</span>
            <span id="animated-name">
              <span>Akshat Gupta</span>
              <span>अक्षत गुप्ता</span>
            </span>
          </h2>
          <p class="wow fadeInLeft dark-text" data-wow-duration="1s" data-wow-delay="0.7s">
            <span style="color: #b42c1d; font-weight: bold;">AI & ML Enthusiast</span> | Innovator
          </p>
          <p class="banner-text wow fadeInLeft" data-wow-duration="1s" data-wow-delay="0.9s">
            Explore my portfolio to see the latest projects where I applied AI to drive real-world impact and bring innovation to various domains.
          </p>

          <!-- Skill Highlights -->
          <div class="banner-skills wow fadeInLeft" data-wow-duration="1s" data-wow-delay="1.1s">
            <span class="banner-skill-badge">AI</span>
            <span class="banner-skill-badge">ML</span>
            <span class="banner-skill-badge">Deep Learning</span>
            <span class="banner-skill-badge">NLP</span>
            <span class="banner-skill-badge">Computer Vision</span>
            <span class="banner-skill-badge">Generative AI</span>
          </div>

          <!-- Action Buttons -->
          <div class="banner-buttons wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.3s">
            <a href="#projects" class="banner-btn primary-btn">
              <i class="fas fa-code"></i> View My Projects
            </a>
            <a href="#contact" class="banner-btn secondary-btn">
              <i class="fas fa-paper-plane"></i> Contact Me
            </a>
          </div>

          <!-- Social Media Links -->
          <div class="banner-social-links wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.5s">
            <a href="https://github.com/Akshat-Gupta04" target="_blank" class="social-icon">
              <i class="fab fa-github"></i>
            </a>
            <a href="www.linkedin.com/in/akshat-gupta-077a7b256" target="_blank" class="social-icon">
              <i class="fab fa-linkedin-in"></i>
            </a>
            <a href="mailto:<EMAIL>" class="social-icon">
              <i class="fas fa-envelope"></i>
            </a>
          </div>
        </div>

        <!-- Image Section (Right Side) -->
        <div class="col-lg-6 wow fadeInRight" data-wow-duration="1s" data-wow-delay="0.5s">
          <div class="profile-image-container">
            <div class="profile-frame"></div>
            <div class="profile-glow"></div>
            <img id="mainProfilePic" src="" alt="Profile Image" class="banner-image img-fluid">
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- ***** End Main Banner ***** -->
<!-- ***** About Section ***** -->
<!-- Desktop About Section (visible on medium and large screens) -->
<section id="about" class="about section d-none d-md-block">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
        About Me
      </h2>
      <p class="section-subtitle wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.5s">
        <span class="code-keyword">&lt;AI_Enthusiast&gt;</span> • <span class="code-variable">Innovator</span> • <span class="code-function">Lifelong_Learner()</span>
      </p>
    </div>
    <div class="about-card-wrapper wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.7s">
      <div class="about-card">
        <div class="about-image">
          <img id="aboutProfilePic" src="" alt="Profile Image" class="img-fluid">
        </div>
        <div class="about-info">
          <h3>Welcome to my AI journey</h3>
          <p>
            I am an enthusiastic AI student dedicated to artificial intelligence, machine learning, deep learning, and generative AI. Currently pursuing a B.Tech in Computer Science & Engineering at Bennett University, I develop innovative solutions that combine technical expertise with creative thinking. I constantly explore emerging trends, dive into new projects, and nurture my hobbies, all of which fuel my passion for advancing AI technology.
          </p>
          <a href="#contact" class="btn about-cta">Let’s Connect</a>
          <button class="btn about-cta" data-bs-toggle="modal" data-bs-target="#resumeModal">
            View My Resume
          </button>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Mobile About Section (visible only on small screens) -->
<section id="about-mobile" class="about-mobile section d-block d-md-none">
  <div class="container">
    <div class="text-center mb-4">
      <h2 class="section-title-mobile wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
        About Me
      </h2>
      <div class="mobile-subtitle wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.5s">
        <span class="mobile-tag">AI Enthusiast</span>
        <span class="mobile-tag">Innovator</span>
        <span class="mobile-tag">Learner</span>
      </div>
    </div>

    <div class="mobile-about-card wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.7s">
      <!-- Profile Image with Glowing Effect -->
      <div class="mobile-profile-container">
        <div class="mobile-profile-glow"></div>
        <img id="mobileAboutProfilePic" src="" alt="Profile Image" class="mobile-profile-img">
      </div>

      <!-- About Content -->
      <div class="mobile-about-content">
        <h3>Welcome to my AI journey</h3>
        <p>
          I am an enthusiastic AI student dedicated to artificial intelligence, machine learning, deep learning, and generative AI. Currently pursuing a B.Tech in Computer Science & Engineering at Bennett University.
        </p>

        <!-- Skills Pills -->
        <div class="mobile-skills">
          <span class="mobile-skill-pill">Machine Learning</span>
          <span class="mobile-skill-pill">Deep Learning</span>
          <span class="mobile-skill-pill">Computer Vision</span>
          <span class="mobile-skill-pill">NLP</span>
        </div>

        <!-- Action Buttons -->
        <div class="mobile-actions">
          <a href="#contact" class="mobile-btn primary-btn">
            <i class="fas fa-paper-plane"></i> Connect
          </a>
          <button class="mobile-btn secondary-btn" data-bs-toggle="modal" data-bs-target="#resumeModal">
            <i class="fas fa-file-alt"></i> Resume
          </button>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Resume Modal -->

<!-- Resume Modal -->
<div class="modal fade" id="resumeModal" tabindex="-1" aria-labelledby="resumeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="resumeModalLabel">My Resume</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <!-- Replace 'images/resume.png' with the path to your resume image -->
        <img src="{{ url_for('static', filename='images/Resume.jpg') }}" alt="Resume" class="img-fluid">
      </div>
      <div class="modal-footer">
        <!-- Download Resume Button -->
        <a href="{{ url_for('static', filename='Resume_AkshatGupta.pdf') }}" download class="btn about-cta">
          Download Resume
        </a>
        <button type="button" class="btn about-cta" data-bs-dismiss="modal">
          Close
        </button>
      </div>
    </div>
  </div>
</div>
  <!-- ***** End About Section ***** -->


  <!-- ***** Enhanced Academic Highlights Section ***** -->
<section id="academics" class="academics section">
  <!-- Neural Network Background -->
  <div class="academics-neural-bg">
    <canvas id="academicsNetworkCanvas"></canvas>
  </div>

  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
        Academic Journey
      </h2>
      <p class="section-subtitle wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.5s">
        My educational background and academic achievements in the field of Computer Science and Artificial Intelligence.
      </p>
    </div>

    <!-- Academic Timeline -->
    <div class="academic-timeline wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
      <div class="timeline-track"></div>

      <!-- Current University -->
      <div class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-arrow"></div>
        <div class="timeline-content">
          <div class="timeline-card">
            <div class="timeline-year">2022 - 2026</div>
            <h3 class="timeline-title">Bennett University</h3>
            <h4 class="timeline-subtitle">B.Tech, Computer Science & Engineering</h4>
            <p class="timeline-description">
              Pursuing a specialized curriculum focused on AI and Machine Learning, with hands-on experience in building advanced models and architectures from scratch.
            </p>
            <div class="academic-stats">
              <div class="academic-stat">
                <div class="stat-icon"><i class="fas fa-graduation-cap"></i></div>
                <div class="stat-info">
                  <div class="stat-label">Current CGPA</div>
                  <div class="stat-value">8.64 / 10</div>
                </div>
              </div>
              <div class="academic-stat">
                <div class="stat-icon"><i class="fas fa-award"></i></div>
                <div class="stat-info">
                  <div class="stat-label">Academic Standing</div>
                  <div class="stat-value">Top 10%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Higher Secondary -->
      <div class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-arrow"></div>
        <div class="timeline-content">
          <div class="timeline-card">
            <div class="timeline-year">2022</div>
            <h3 class="timeline-title">Higher Secondary Education</h3>
            <h4 class="timeline-subtitle">CBSE Board</h4>
            <p class="timeline-description">
              Completed higher secondary education with a focus on Mathematics, Physics, and Computer Science, laying a strong foundation for advanced studies in AI and ML.
            </p>
            <div class="academic-stats">
              <div class="academic-stat">
                <div class="stat-icon"><i class="fas fa-percentage"></i></div>
                <div class="stat-info">
                  <div class="stat-label">Overall Percentage</div>
                  <div class="stat-value">80%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Secondary -->
      <div class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-arrow"></div>
        <div class="timeline-content">
          <div class="timeline-card">
            <div class="timeline-year">2020</div>
            <h3 class="timeline-title">Secondary Education</h3>
            <h4 class="timeline-subtitle">CBSE Board</h4>
            <p class="timeline-description">
              Achieved excellence in secondary education, demonstrating strong analytical skills and a passion for technology and mathematics.
            </p>
            <div class="academic-stats">
              <div class="academic-stat">
                <div class="stat-icon"><i class="fas fa-percentage"></i></div>
                <div class="stat-info">
                  <div class="stat-label">Overall Percentage</div>
                  <div class="stat-value">95.2%</div>
                </div>
              </div>
              <div class="academic-stat">
                <div class="stat-icon"><i class="fas fa-medal"></i></div>
                <div class="stat-info">
                  <div class="stat-label">School Rank</div>
                  <div class="stat-value">Top 5</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Academic Grid Cards -->
    <div class="academic-grid">
      <!-- Key Courses -->
      <div class="academic-grid-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
        <div class="grid-card-icon">
          <i class="fas fa-book-open"></i>
        </div>
        <h3 class="grid-card-title">Key Courses</h3>
        <div class="grid-card-content">
          <ul class="course-list">
            <li class="course-item">
              <i class="fas fa-check-circle course-icon"></i>
              <span class="course-name">Advanced Machine Learning</span>
            </li>
            <li class="course-item">
              <i class="fas fa-check-circle course-icon"></i>
              <span class="course-name">Deep Learning Architectures</span>
            </li>
            <li class="course-item">
              <i class="fas fa-check-circle course-icon"></i>
              <span class="course-name">Natural Language Processing</span>
            </li>
            <li class="course-item">
              <i class="fas fa-check-circle course-icon"></i>
              <span class="course-name">Computer Vision</span>
            </li>
            <li class="course-item">
              <i class="fas fa-check-circle course-icon"></i>
              <span class="course-name">Data Structures & Algorithms</span>
            </li>
            <li class="course-item">
              <i class="fas fa-check-circle course-icon"></i>
              <span class="course-name">Probability & Statistics</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Academic Achievements -->
      <div class="academic-grid-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
        <div class="grid-card-icon">
          <i class="fas fa-trophy"></i>
        </div>
        <h3 class="grid-card-title">Academic Achievements</h3>
        <div class="grid-card-content">
          <p>Consistently maintained a strong academic record while actively participating in AI research and development projects.</p>
          <div class="grid-card-highlight">8.64</div>
          <p>Current CGPA at Bennett University, placing me in the top 10% of my class. Received recognition for outstanding performance in AI and ML courses.</p>
        </div>
      </div>

      <!-- Research Interests -->
      <div class="academic-grid-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
        <div class="grid-card-icon">
          <i class="fas fa-lightbulb"></i>
        </div>
        <h3 class="grid-card-title">Research Interests</h3>
        <div class="grid-card-content">
          <p>My academic journey has shaped my research interests in several cutting-edge areas of AI and ML:</p>
          <div class="interest-tags">
            <div class="interest-tag">Generative AI</div>
            <div class="interest-tag">Multimodal Learning</div>
            <div class="interest-tag">Retrieval-Augmented Generation</div>
            <div class="interest-tag">Computer Vision</div>
            <div class="interest-tag">Model Architecture Design</div>
            <div class="interest-tag">Parameter-Efficient Fine-Tuning</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

  <!-- ***** Enhanced Skills Section ***** -->
<section id="skills" class="skills section">
  <!-- Neural Network Background -->
  <div class="skills-neural-bg">
    <canvas id="skillsNetworkCanvas"></canvas>
  </div>

  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
        Technical Expertise
      </h2>
      <p class="section-subtitle wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.5s">
        My knowledge and capabilities in AI, machine learning, and advanced technologies, with a focus on practical implementation and problem-solving.
      </p>
    </div>

    <!-- Skills Categories Filter -->
    <div class="skills-categories wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
      <div class="skill-category active" data-category="all">All Skills</div>
      <div class="skill-category" data-category="ai-ml">AI & ML</div>
      <div class="skill-category" data-category="generative">Generative AI</div>
      <div class="skill-category" data-category="nlp-cv">NLP & Computer Vision</div>
    </div>

    <!-- Advanced Skills Grid -->
    <div class="skills-grid">
      <!-- AI & Machine Learning -->
      <div class="advanced-skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s" data-category="ai-ml">
        <div class="skill-category-badge">AI & ML</div>
        <div class="skill-header">
          <div class="skill-icon">
            <img src="https://cdn-icons-png.flaticon.com/512/2103/2103787.png" alt="AI & ML">
          </div>
          <div class="skill-title">
            <h3>AI & Machine Learning</h3>
            <div class="skill-level">Expert</div>
          </div>
        </div>
        <div class="skill-description">
          Comprehensive expertise in machine learning algorithms, deep learning architectures, and advanced AI techniques. Skilled in implementing Hierarchical RAG systems, model optimization, and developing custom AI solutions for complex problems.
        </div>
        <div class="skill-progress">
          <div class="progress-bar" style="width: 95%;"></div>
        </div>
        <div class="skill-mastery">
          <div class="mastery-level">
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
          </div>
          <div class="mastery-text">95%</div>
        </div>
        <div class="skill-tags">
          <div class="skill-tag">Deep Learning</div>
          <div class="skill-tag">Neural Networks</div>
          <div class="skill-tag">Hierarchical RAG</div>
          <div class="skill-tag">Model Optimization</div>
          <div class="skill-tag">Multimodal AI</div>
        </div>
      </div>

      <!-- Generative AI -->
      <div class="advanced-skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.4s" data-category="generative">
        <div class="skill-category-badge">Generative AI</div>
        <div class="skill-header">
          <div class="skill-icon">
            <img src="https://cdn-icons-png.flaticon.com/512/7017/7017303.png" alt="Generative AI">
          </div>
          <div class="skill-title">
            <h3>Generative AI</h3>
            <div class="skill-level">Expert</div>
          </div>
        </div>
        <div class="skill-description">
          Proficient in fine-tuning LLMs and image generation models using advanced techniques. Experienced with diffusion models, VAEs, LoRA, Q-LoRA, and parameter-efficient fine-tuning methods. Skilled in working with small language models (SLMs) and vision-language models (VLMs) for multimodal applications.
        </div>
        <div class="skill-progress">
          <div class="progress-bar" style="width: 95%;"></div>
        </div>
        <div class="skill-mastery">
          <div class="mastery-level">
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
          </div>
          <div class="mastery-text">95%</div>
        </div>
        <div class="skill-tags">
          <div class="skill-tag">LLM Fine-Tuning</div>
          <div class="skill-tag">Image Generation Models</div>
          <div class="skill-tag">Small Language Models</div>
          <div class="skill-tag">Vision-Language Models</div>
          <div class="skill-tag">LoRA & Q-LoRA</div>
        </div>
      </div>

      <!-- Natural Language Processing -->
      <div class="advanced-skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s" data-category="nlp-cv">
        <div class="skill-category-badge">NLP & Computer Vision</div>
        <div class="skill-header">
          <div class="skill-icon">
            <img src="https://cdn-icons-png.flaticon.com/512/2103/2103633.png" alt="NLP">
          </div>
          <div class="skill-title">
            <h3>Natural Language Processing</h3>
            <div class="skill-level">Advanced</div>
          </div>
        </div>
        <div class="skill-description">
          Knowledge of large language model architectures, small language models, and fine-tuning techniques. Experienced with attention mechanisms, tokenization, and training pipelines for NLP tasks including translation, summarization, and text generation.
        </div>
        <div class="skill-progress">
          <div class="progress-bar" style="width: 90%;"></div>
        </div>
        <div class="skill-mastery">
          <div class="mastery-level">
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
          </div>
          <div class="mastery-text">90%</div>
        </div>
        <div class="skill-tags">
          <div class="skill-tag">LLM Fine-Tuning</div>
          <div class="skill-tag">Small Language Models</div>
          <div class="skill-tag">Transformers</div>
          <div class="skill-tag">Attention Mechanisms</div>
          <div class="skill-tag">Text Generation</div>
        </div>
      </div>

      <!-- Computer Vision -->
      <div class="advanced-skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.6s" data-category="nlp-cv">
        <div class="skill-category-badge">NLP & Computer Vision</div>
        <div class="skill-header">
          <div class="skill-icon">
            <img src="https://cdn-icons-png.flaticon.com/512/1680/1680292.png" alt="Computer Vision">
          </div>
          <div class="skill-title">
            <h3>Computer Vision</h3>
            <div class="skill-level">Expert</div>
          </div>
        </div>
        <div class="skill-description">
          Expertise in computer vision techniques including object detection, image segmentation, and facial recognition. Proficient in implementing Vision Transformers, Detection Transformers, and YOLO architectures. Experienced with Vision-Language Models (VLMs) and integrating computer vision with generative AI.
        </div>
        <div class="skill-progress">
          <div class="progress-bar" style="width: 95%;"></div>
        </div>
        <div class="skill-mastery">
          <div class="mastery-level">
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
          </div>
          <div class="mastery-text">95%</div>
        </div>
        <div class="skill-tags">
          <div class="skill-tag">Vision Transformers</div>
          <div class="skill-tag">Vision-Language Models</div>
          <div class="skill-tag">Object Detection</div>
          <div class="skill-tag">YOLO</div>
          <div class="skill-tag">Image Segmentation</div>
        </div>
      </div>

      <!-- Programming & Frameworks -->
      <div class="advanced-skill-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s" data-category="ai-ml">
        <div class="skill-category-badge">AI & ML</div>
        <div class="skill-header">
          <div class="skill-icon">
            <img src="https://cdn-icons-png.flaticon.com/512/5968/5968350.png" alt="Programming">
          </div>
          <div class="skill-title">
            <h3>Programming & Frameworks</h3>
            <div class="skill-level">Expert</div>
          </div>
        </div>
        <div class="skill-description">
          Proficient in Python and various AI frameworks including PyTorch, TensorFlow, LangChain, and Diffusers. Experienced with CUDA for GPU acceleration and optimization of deep learning models. Skilled in developing end-to-end AI solutions from data preprocessing to deployment.
        </div>
        <div class="skill-progress">
          <div class="progress-bar" style="width: 95%;"></div>
        </div>
        <div class="skill-mastery">
          <div class="mastery-level">
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
            <div class="mastery-dot filled"></div>
          </div>
          <div class="mastery-text">95%</div>
        </div>
        <div class="skill-tags">
          <div class="skill-tag">Python</div>
          <div class="skill-tag">PyTorch</div>
          <div class="skill-tag">TensorFlow</div>
          <div class="skill-tag">LangChain</div>
          <div class="skill-tag">Diffusers</div>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- ***** End Skills Section ***** -->

<!-- ***** Projects Section ***** -->

<div id="projects" class="ai-projects section">
  <!-- Neural Network Background -->
  <div class="neural-network-bg">
    <canvas id="neuralNetworkCanvas"></canvas>
  </div>

  <div class="container">
    <div class="section-header text-center">
      <h2 class="section-title wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
        My AI Projects
      </h2>
      <p class="section-subtitle wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.5s">
        Explore my portfolio of AI and machine learning projects that demonstrate innovative solutions and technical expertise.
      </p>
    </div>

    <!-- Desktop Projects Grid (Visible on large screens) -->
    <div class="projects-grid d-none d-lg-grid">
      <!-- Featured Project (Medical ChatBot) -->
      <div class="ai-project-card featured-project wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
        <div class="featured-badge">
          <i class="fas fa-star"></i> Featured
        </div>
        <div class="project-type">RAG System</div>
        <div class="project-image-container">
          <img src="{{ url_for('static', filename='images/medical_chatbot.png') }}" alt="Medical ChatBot" class="project-image">
        </div>
        <div class="project-content">
          <h3 class="project-title">Medical ChatBot</h3>
          <p class="project-description">
            A context-aware medical assistant that leverages retrieval-augmented generation with a locally stored vector database (built from PDFs) to deliver dynamic, AI-powered medical advice through a modern, conversational interface.
          </p>
          <div class="tech-stack">
            <span class="tech-tag">RAG</span>
            <span class="tech-tag">LangChain</span>
            <span class="tech-tag">Vector DB</span>
            <span class="tech-tag">Generative AI</span>
            <span class="tech-tag">Python</span>
          </div>
          <div class="project-links">
            <a href="https://github.com/Akshat-Gupta04/Medical-ChatBOT" class="github-link" target="_blank">
              <i class="fab fa-github"></i> View on GitHub
            </a>
          </div>
        </div>
      </div>

      <!-- Project 1: Fine-Tuned LoRA Model -->
      <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.4s">
        <div class="project-type">Generative AI</div>
        <div class="project-image-container">
          <img src="{{ url_for('static', filename='images/829342611589419847.png') }}" alt="LoRA Model" class="project-image">
        </div>
        <div class="project-content">
          <h3 class="project-title">Fine-Tuned LoRA Model</h3>
          <p class="project-description">
            A custom LoRA model fine-tuned with my own face, trained on Flux models as the base. It generates realistic, high-quality images that showcase the power of generative AI and deep learning.
          </p>
          <div class="tech-stack">
            <span class="tech-tag">LoRA</span>
            <span class="tech-tag">Generative AI</span>
            <span class="tech-tag">Flux Models</span>
            <span class="tech-tag">Diffusion</span>
          </div>
        </div>
      </div>

      <!-- Project 2: Eye Disease Detection -->
      <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
        <div class="project-type">Computer Vision</div>
        <div class="project-image-container">
          <img src="{{ url_for('static', filename='images/eye_detection.png') }}" alt="Eye Disease Detection" class="project-image">
        </div>
        <div class="project-content">
          <h3 class="project-title">Eye Disease Detection</h3>
          <p class="project-description">
            Empowering precise eye disease detection through cutting-edge generative AI and deep learning on fundus images, improving medical diagnostics with AI.
          </p>
          <div class="tech-stack">
            <span class="tech-tag">Deep Learning</span>
            <span class="tech-tag">Computer Vision</span>
            <span class="tech-tag">Medical AI</span>
            <span class="tech-tag">Python</span>
          </div>
          <div class="project-links">
            <a href="https://github.com/Akshat-Gupta04/FaceCheck_complete.git" class="github-link" target="_blank">
              <i class="fab fa-github"></i> View on GitHub
            </a>
          </div>
        </div>
      </div>

      <!-- Project 3: Akshar -->
      <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.6s">
        <div class="project-type">Mobile App</div>
        <div class="project-image-container">
          <img src="{{ url_for('static', filename='images/Akshar.png') }}" alt="Akshar" class="project-image">
        </div>
        <div class="project-content">
          <h3 class="project-title">Akshar</h3>
          <p class="project-description">
            A chat app that translates text and audio messages in real time, built using Flutter. Enables seamless communication across language barriers with AI-powered translation.
          </p>
          <div class="tech-stack">
            <span class="tech-tag">Flutter</span>
            <span class="tech-tag">Dart</span>
            <span class="tech-tag">NLP</span>
            <span class="tech-tag">Translation AI</span>
          </div>
          <div class="project-links">
            <a href="https://github.com/Akshat-Gupta04/AksharApp.git" class="github-link" target="_blank">
              <i class="fab fa-github"></i> View on GitHub
            </a>
          </div>
        </div>
      </div>
      <!-- Project 4: FacePay -->
      <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
        <div class="project-type">Facial Recognition</div>
        <div class="project-image-container">
          <img src="{{ url_for('static', filename='images/FacePay.png') }}" alt="FacePay" class="project-image">
        </div>
        <div class="project-content">
          <h3 class="project-title">FacePay</h3>
          <p class="project-description">
            An AI-based payment system using facial recognition for secure UPI transactions, combining biometric security with financial technology.
          </p>
          <div class="tech-stack">
            <span class="tech-tag">DeepFace</span>
            <span class="tech-tag">Flask</span>
            <span class="tech-tag">SQLite3</span>
            <span class="tech-tag">Biometrics</span>
          </div>
          <div class="project-links">
            <a href="https://github.com/Akshat-Gupta04/FacePay.git" class="github-link" target="_blank">
              <i class="fab fa-github"></i> View on GitHub
            </a>
          </div>
        </div>
      </div>

      <!-- Project 5: Vision Transformer -->
      <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.8s">
        <div class="project-type">Transformers</div>
        <div class="project-image-container">
          <img src="{{ url_for('static', filename='images/vision.png') }}" alt="Vision Transformer" class="project-image">
        </div>
        <div class="project-content">
          <h3 class="project-title">Vision Transformer for MNIST</h3>
          <p class="project-description">
            An implementation of a Vision Transformer (ViT) built from scratch for image classification on the MNIST dataset, featuring custom encoder layers with multi-head self-attention.
          </p>
          <div class="tech-stack">
            <span class="tech-tag">Vision Transformer</span>
            <span class="tech-tag">PyTorch</span>
            <span class="tech-tag">Self-Attention</span>
            <span class="tech-tag">MNIST</span>
          </div>
          <div class="project-links">
            <a href="https://github.com/Akshat-Gupta04/Vision-Transformers-from-scratch.git" class="github-link" target="_blank">
              <i class="fab fa-github"></i> View on GitHub
            </a>
          </div>
        </div>
      </div>


      <!-- Project 6: Custom Emotion Generator -->
      <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.9s">
        <div class="project-type">Generative AI</div>
        <div class="project-image-container">
          <img src="{{ url_for('static', filename='images/custom_emotion.png') }}" alt="Custom Emotion Generator" class="project-image">
        </div>
        <div class="project-content">
          <h3 class="project-title">Custom Emotion Generator</h3>
          <p class="project-description">
            A tool that animates expressive facial expressions from a single reference image, allowing users to create various emotional expressions through an intuitive interface.
          </p>
          <div class="tech-stack">
            <span class="tech-tag">PyTorch</span>
            <span class="tech-tag">Computer Vision</span>
            <span class="tech-tag">CUDA</span>
            <span class="tech-tag">Gradio</span>
          </div>
          <div class="project-links">
            <a href="https://github.com/Akshat-Gupta04/Custom-Emotion-Generator.git" class="github-link" target="_blank">
              <i class="fab fa-github"></i> View on GitHub
            </a>
          </div>
        </div>
      </div>

      <!-- Project 7: Teacher-Student Distillation on IMDB -->
      <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.0s">
        <div class="project-type">Sentiment Analysis</div>
        <div class="project-image-container">
          <img src="{{ url_for('static', filename='images/sentiment.jpg') }}" alt="Teacher-Student Distillation" class="project-image">
        </div>
        <div class="project-content">
          <h3 class="project-title">Teacher-Student Distillation on IMDB</h3>
          <p class="project-description">
            This project demonstrates teacher–student distillation on the IMDB movie sentiment dataset.
          <div class="tech-stack">
            <span class="tech-tag">Teacher-Student Distillation</span>
            <span class="tech-tag">IMDB Sentiment Analysis</span>
            <span class="tech-tag">Hugging Face</span>
            <span class="tech-tag">Transformers</span>
          </div>
          <div class="project-links">
            <a href="https://github.com/Akshat-Gupta04/Teacher-Student-Distillation-on-IMDB-Movie-Sentiment-Reviews" class="github-link" target="_blank" rel="noopener noreferrer">
              <i class="fab fa-github"></i> View on GitHub
            </a>
          </div>
        </div>
      </div>

    </div>

    <!-- View All Projects Button -->
    <div class="view-all-projects wow fadeInUp" data-wow-duration="1s" data-wow-delay="1s">
      <a href="https://github.com/Akshat-Gupta04?tab=repositories" class="view-all-btn" target="_blank">
        <i class="fas fa-code-branch"></i> View All Projects
      </a>
    </div>
    <!-- End Desktop Grid Layout -->

    <!-- Mobile Projects Carousel (Visible only on screens smaller than large) -->
    <div class="mobile-projects-carousel d-lg-none">
      <div class="carousel-container">
        <!-- Featured Project (Medical ChatBot) -->
        <div class="carousel-slide">
          <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.3s">
            <div class="featured-badge">
              <i class="fas fa-star"></i> Featured
            </div>
            <div class="project-type">RAG System</div>
            <div class="project-image-container">
              <img src="{{ url_for('static', filename='images/medical_chatbot.png') }}" alt="Medical ChatBot" class="project-image">
            </div>
            <div class="project-content">
              <h3 class="project-title">Medical ChatBot</h3>
              <p class="project-description">
                A context-aware medical assistant that leverages retrieval-augmented generation for AI-powered medical advice.
              </p>
              <div class="tech-stack">
                <span class="tech-tag">RAG</span>
                <span class="tech-tag">LangChain</span>
                <span class="tech-tag">Python</span>
              </div>
              <div class="project-links">
                <a href="https://github.com/Akshat-Gupta04/Medical-ChatBOT" class="github-link" target="_blank">
                  <i class="fab fa-github"></i> View on GitHub
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Project 1: Fine-Tuned LoRA Model -->
        <div class="carousel-slide">
          <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.4s">
            <div class="project-type">Generative AI</div>
            <div class="project-image-container">
              <img src="{{ url_for('static', filename='images/829342611589419847.png') }}" alt="LoRA Model" class="project-image">
            </div>
            <div class="project-content">
              <h3 class="project-title">Fine-Tuned LoRA Model</h3>
              <p class="project-description">
                A custom LoRA model fine-tuned with my own face, trained on Flux models as the base.
              </p>
              <div class="tech-stack">
                <span class="tech-tag">LoRA</span>
                <span class="tech-tag">Generative AI</span>
                <span class="tech-tag">Diffusion</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Project 2: Eye Disease Detection -->
        <div class="carousel-slide">
          <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
            <div class="project-type">Computer Vision</div>
            <div class="project-image-container">
              <img src="{{ url_for('static', filename='images/eye_detection.png') }}" alt="Eye Disease Detection" class="project-image">
            </div>
            <div class="project-content">
              <h3 class="project-title">Eye Disease Detection</h3>
              <p class="project-description">
                Empowering precise eye disease detection through cutting-edge generative AI and deep learning.
              </p>
              <div class="tech-stack">
                <span class="tech-tag">Deep Learning</span>
                <span class="tech-tag">Medical AI</span>
                <span class="tech-tag">Python</span>
              </div>
              <div class="project-links">
                <a href="https://github.com/Akshat-Gupta04/FaceCheck_complete.git" class="github-link" target="_blank">
                  <i class="fab fa-github"></i> View on GitHub
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Project 3: Akshar -->
        <div class="carousel-slide">
          <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.6s">
            <div class="project-type">Mobile App</div>
            <div class="project-image-container">
              <img src="{{ url_for('static', filename='images/Akshar.png') }}" alt="Akshar" class="project-image">
            </div>
            <div class="project-content">
              <h3 class="project-title">Akshar</h3>
              <p class="project-description">
                A chat app that translates text and audio messages in real time, built using Flutter.
              </p>
              <div class="tech-stack">
                <span class="tech-tag">Flutter</span>
                <span class="tech-tag">Dart</span>
                <span class="tech-tag">NLP</span>
              </div>
              <div class="project-links">
                <a href="https://github.com/Akshat-Gupta04/AksharApp.git" class="github-link" target="_blank">
                  <i class="fab fa-github"></i> View on GitHub
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Project 4: FacePay -->
        <div class="carousel-slide">
          <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.7s">
            <div class="project-type">Facial Recognition</div>
            <div class="project-image-container">
              <img src="{{ url_for('static', filename='images/FacePay.png') }}" alt="FacePay" class="project-image">
            </div>
            <div class="project-content">
              <h3 class="project-title">FacePay</h3>
              <p class="project-description">
                An AI-based payment system using facial recognition for secure UPI transactions.
              </p>
              <div class="tech-stack">
                <span class="tech-tag">DeepFace</span>
                <span class="tech-tag">Flask</span>
                <span class="tech-tag">Biometrics</span>
              </div>
              <div class="project-links">
                <a href="https://github.com/Akshat-Gupta04/FacePay.git" class="github-link" target="_blank">
                  <i class="fab fa-github"></i> View on GitHub
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Project 5: Vision Transformer -->
        <div class="carousel-slide">
          <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.8s">
            <div class="project-type">Transformers</div>
            <div class="project-image-container">
              <img src="{{ url_for('static', filename='images/vision.png') }}" alt="Vision Transformer" class="project-image">
            </div>
            <div class="project-content">
              <h3 class="project-title">Vision Transformer</h3>
              <p class="project-description">
                A Vision Transformer (ViT) built from scratch for image classification on the MNIST dataset.
              </p>
              <div class="tech-stack">
                <span class="tech-tag">Vision Transformer</span>
                <span class="tech-tag">PyTorch</span>
                <span class="tech-tag">MNIST</span>
              </div>
              <div class="project-links">
                <a href="https://github.com/Akshat-Gupta04/Vision-Transformers-from-scratch.git" class="github-link" target="_blank">
                  <i class="fab fa-github"></i> View on GitHub
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Project 6: Custom Emotion Generator -->
        <div class="carousel-slide">
          <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.9s">
            <div class="project-type">Generative AI</div>
            <div class="project-image-container">
              <img src="{{ url_for('static', filename='images/custom_emotion.png') }}" alt="Custom Emotion Generator" class="project-image">
            </div>
            <div class="project-content">
              <h3 class="project-title">Custom Emotion Generator</h3>
              <p class="project-description">
                A tool that animates expressive facial expressions from a single reference image.
              </p>
              <div class="tech-stack">
                <span class="tech-tag">PyTorch</span>
                <span class="tech-tag">Computer Vision</span>
                <span class="tech-tag">CUDA</span>
              </div>
              <div class="project-links">
                <a href="https://github.com/Akshat-Gupta04/Custom-Emotion-Generator.git" class="github-link" target="_blank">
                  <i class="fab fa-github"></i> View on GitHub
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Project 7: Teacher-Student Distillation on IMDB -->
        <div class="carousel-slide">
          <div class="ai-project-card wow fadeInUp" data-wow-duration="1s" data-wow-delay="1.0s">
            <div class="project-type">Sentiment Analysis</div>
            <div class="project-image-container">
              <img src="{{ url_for('static', filename='images/sentiment.jpg') }}" alt="Teacher-Student Distillation" class="project-image">
            </div>
            <div class="project-content">
              <h3 class="project-title">Teacher-Student Distillation on IMDB</h3>
              <p class="project-description">
                A project demonstrating teacher-student distillation on the IMDB movie sentiment dataset for efficient model compression.
              </p>
              <div class="tech-stack">
                <span class="tech-tag">Sentiment Analysis</span>
                <span class="tech-tag">Hugging Face</span>
                <span class="tech-tag">Transformers</span>
              </div>
              <div class="project-links">
                <a href="https://github.com/Akshat-Gupta04/Teacher-Student-Distillation-on-IMDB-Movie-Sentiment-Reviews" class="github-link" target="_blank" rel="noopener noreferrer">
                  <i class="fab fa-github"></i> View on GitHub
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Carousel Controls -->
      <div class="carousel-controls">
        <button class="carousel-btn" id="prevCarouselBtn">
          <i class="fas fa-chevron-left"></i>
        </button>
        <button class="carousel-btn" id="nextCarouselBtn">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>

      <!-- Carousel Dots -->
      <div class="carousel-dots"></div>
    </div>
    <!-- End Mobile Projects Carousel -->
  </div>
</div>
<!-- ***** End Projects Section ***** -->

 <!-- Certificates Section -->
<!-- Certificates Section -->
<section id="certificates" class="certificates section">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.3s">
        Certificates
      </h2>
      <p class="section-subtitle wow fadeInUp" data-wow-duration="1.5s" data-wow-delay="0.5s">
        Browse through my certifications.
      </p>
    </div>
    <!-- Bootstrap Carousel for Certificates (1 per slide) -->
    <div id="certificatesCarousel" class="carousel slide" data-bs-ride="carousel">
      <!-- Carousel Indicators -->
      <div class="carousel-indicators">
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="3" aria-label="Slide 4"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="4" aria-label="Slide 5"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="5" aria-label="Slide 6"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="6" aria-label="Slide 7"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="7" aria-label="Slide 8"></button>
        <button type="button" data-bs-target="#certificatesCarousel" data-bs-slide-to="8" aria-label="Slide 9"></button>
      </div>

      <div class="carousel-inner">
        <!-- Slide 1: Certificate 1 -->
        <div class="carousel-item active">
          <img src="{{ url_for('static', filename='images/cert1.png') }}" class="d-block w-100 certificate-img" alt="Certificate 1" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 2: Certificate 2 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert2.png') }}" class="d-block w-100 certificate-img" alt="Certificate 2" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 3: Certificate 3 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert3.png') }}" class="d-block w-100 certificate-img" alt="Certificate 3" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 4: Certificate 4 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert4.png') }}" class="d-block w-100 certificate-img" alt="Certificate 4" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 5: Certificate 5 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert5.png') }}" class="d-block w-100 certificate-img" alt="Certificate 5" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 6: Certificate 6 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert6.png') }}" class="d-block w-100 certificate-img" alt="Certificate 6" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 7: Certificate 7 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert7.png') }}" class="d-block w-100 certificate-img" alt="Certificate 7" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 8: Certificate 8 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert8.png') }}" class="d-block w-100 certificate-img" alt="Certificate 8" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
        <!-- Slide 9: Certificate 9 -->
        <div class="carousel-item">
          <img src="{{ url_for('static', filename='images/cert9.png') }}" class="d-block w-100 certificate-img" alt="Certificate 9" style="max-height: 400px; object-fit: contain; cursor: pointer;">
        </div>
      </div>
      <!-- Carousel Controls -->
      <button class="carousel-control-prev" type="button" data-bs-target="#certificatesCarousel" data-bs-slide="prev">
        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
        <span class="visually-hidden">Previous</span>
      </button>
      <button class="carousel-control-next" type="button" data-bs-target="#certificatesCarousel" data-bs-slide="next">
        <span class="carousel-control-next-icon" aria-hidden="true"></span>
        <span class="visually-hidden">Next</span>
      </button>
    </div>
  </div>
</section>

<!-- Certificate Modal -->
<div class="modal fade" id="certificateModal" tabindex="-1" aria-labelledby="certificateModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="certificateModalLabel">Certificate</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center">
        <img src="" alt="Certificate" id="modalCertificateImg" class="img-fluid">
      </div>
    </div>
  </div>
</div>

<!-- Certificate Modal Script -->
<script>
  // Attach click event on all certificate images inside the carousel
  document.querySelectorAll('.certificate-img').forEach(function(img) {
    img.addEventListener('click', function() {
      var src = this.getAttribute('src');
      document.getElementById('modalCertificateImg').setAttribute('src', src);
      var modal = new bootstrap.Modal(document.getElementById('certificateModal'));
      modal.show();
    });
  });
</script>
  <!-- ***** Contact Section ***** -->
   <!-- Floating Blog Button -->
<!-- Floating Blog Button -->
<!-- <div class="floating-blog-btn">
  <a href="{{ url_for('blog') }}" class="btn btn-blog">
    <i class="fas fa-blog"></i> My Blogs
  </a>
</div> -->
 <!-- Contact Section -->
 <section id="contact" class="contact section">
  <div class="container">
    <h2 class="section-title wow fadeInDown" data-wow-duration="1s" data-wow-delay="0.3s">
      Get in Touch
    </h2>
    <p class="section-subtitle wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.5s">
      Have a project, question, or collaboration idea? Let’s connect and bring ideas to life.
    </p>

    <div class="contact-container">
      <div class="contact-info wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.4s">
        <h4>Contact Details</h4>
        <div class="contact-item">
          <!-- Replace 'assets/icons/email.svg' with your actual email SVG logo path -->
          <img src="static/images/mail.svg" alt="Email Logo" class="contact-icon">
          <a href="mailto:<EMAIL>" class="contact-email"><EMAIL></a>
        </div>
        <div class="contact-item">
          <!-- Replace 'assets/icons/linkedin.svg' with your actual LinkedIn SVG logo path -->
          <img src="static/images/linkedin.svg" alt="LinkedIn Logo" class="contact-icon">
          <a href="https://www.linkedin.com/in/akshat-gupta-077a7b256/" target="_blank">
            linkedin.com/in/akshat-gupta
          </a>
        </div>
        <div class="contact-item">
          <!-- Replace 'assets/icons/github.svg' with your actual GitHub SVG logo path -->
          <img src="static/images/github.svg" alt="GitHub Logo" class="contact-icon">
          <a href="https://github.com/Akshat-Gupta04" target="_blank">
            github.com/Akshat-Gupta04
          </a>
        </div>
        <div class="contact-item">
          <!-- Replace 'assets/icons/location.svg' with your actual Location SVG logo path -->

          <p>Greater Noida, India</p>
        </div>
      </div>
    </div>

    <p class="final-note wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.6s">
      Looking forward to connecting and exploring new opportunities.
    </p>
  </div>
</section>
  <!-- ***** End Contact Section ***** -->



  <!-- Futuristic Holographic Chat Button -->
  <div class="floating-chat-btn">
    <!-- Animated Floating Message -->
    <div class="floating-chat-message" id="floating-chat-message">
      <div class="message-content">
        <div class="typing-indicator">
          <span></span><span></span><span></span>
        </div>
        <p>Have questions regarding Akshat Gupta and his projects? Click me!</p>
      </div>
      <div class="message-arrow"></div>
    </div>

    <button id="chat-toggle" class="holographic-button">
      <div class="button-content">
        <div class="ai-icon">
          <div class="ai-rings"></div>
          <div class="ai-rings"></div>
          <div class="ai-rings"></div>
        </div>
      </div>
    </button>
  </div>

  <!-- Neural Network Chat Interface -->
  <div id="neural-chat-interface" class="neural-chat-interface">
    <!-- Neural Network Background -->
    <div class="neural-network-bg"></div>

    <!-- Chat Header -->
    <div class="neural-chat-header">
      <div class="neural-chat-title">
        <div class="neural-model-icon"></div>
        <div class="neural-model-info">
          <div class="neural-model-name">Neural Assistant</div>
          <div class="neural-model-status">
            <div class="status-indicator"></div>
            <span>Active</span>
          </div>
        </div>
      </div>
      <div class="neural-chat-controls">
        <button id="minimize-neural-chat" class="neural-control-btn">
          <svg viewBox="0 0 24 24">
            <path d="M18 15L12 9L6 15"></path>
          </svg>
        </button>
        <button id="close-neural-chat" class="neural-control-btn">
          <svg viewBox="0 0 24 24">
            <path d="M18 6L6 18M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Chat Body -->
    <div id="neural-chat-body" class="neural-chat-body">
      <!-- Welcome Message -->
      <div class="neural-welcome">
        <div class="neural-welcome-icon">
          <div class="neural-icon-rings"></div>
          <div class="neural-icon-rings"></div>
          <div class="neural-icon-rings"></div>
        </div>
        <div class="neural-welcome-title">Neural Assistant</div>
        <div class="neural-welcome-subtitle">
          I'm your AI-powered assistant. Ask me anything about this portfolio or AI in general.
        </div>
        <div class="neural-welcome-suggestions">
          <div class="neural-suggestion">Tell me about your projects</div>
          <div class="neural-suggestion">What skills do you have?</div>
          <div class="neural-suggestion">How can I contact you?</div>
        </div>
      </div>
    </div>

    <!-- Chat Input -->
    <div class="neural-chat-input">
      <div class="neural-input-container">
        <textarea id="neural-input-field" class="neural-input-field" placeholder="Ask me anything..." rows="1"></textarea>
        <button id="neural-send-btn" class="neural-send-btn">
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M22 2L11 13M22 2L15 22L11 13L2 9L22 2Z"></path>
          </svg>
        </button>
      </div>
      <div class="neural-input-features">
        <div class="neural-input-info">
          <span>Neural Engine v3.7</span>
        </div>
        <div class="neural-input-actions">
          <div class="neural-action-btn">
            <i class="fas fa-microphone"></i>
          </div>
          <div class="neural-action-btn">
            <i class="fas fa-image"></i>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer>
    <div class="container text-center">
      <p class="wow fadeIn" data-wow-duration="1s" data-wow-delay="0.3s">
        2025 © Akshat Gupta. All Rights Reserved.
      </p>
    </div>
  </footer>
  <!-- Scripts: Load each only once and in the proper order -->
  <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
  <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
  <script src="{{ url_for('static', filename='js/owl-carousel.js') }}"></script>
  <script src="{{ url_for('static', filename='js/animation.js') }}"></script>
  <script src="{{ url_for('static', filename='js/imagesloaded.js') }}"></script>
  <script src="{{ url_for('static', filename='js/custom.js') }}"></script>
  <script src="{{ url_for('static', filename='js/animated-name.js') }}"></script>
  <script src="{{ url_for('static', filename='js/projects-slider.js') }}"></script>
  <script src="{{ url_for('static', filename='js/ai-projects.js') }}"></script>
  <script src="{{ url_for('static', filename='js/modern-certificates.js') }}"></script>
  <script src="{{ url_for('static', filename='js/mobile-certificates.js') }}"></script>
  <script src="{{ url_for('static', filename='js/futuristic-chatbot.js') }}"></script>
  <script src="{{ url_for('static', filename='js/contact-network.js') }}"></script>
  <script src="{{ url_for('static', filename='js/enhanced-navbar.js') }}"></script>
  <script src="{{ url_for('static', filename='js/mobile-banner.js') }}"></script>
  <script src="{{ url_for('static', filename='js/skills-network.js') }}"></script>
  <script src="{{ url_for('static', filename='js/academics-network.js') }}"></script>

  <!-- NEW SPLASH ANIMATION SCRIPT -->
  <script>
  // Track used images
  let usedImages = [];

  // Back-to-Top Button functionality
  var backToTopBtn = document.getElementById("backToTopBtn");
  window.onscroll = function() { scrollFunction(); };
  function scrollFunction() {
    if (document.body.scrollTop > 300 || document.documentElement.scrollTop > 300) {
      backToTopBtn.style.display = "block";
    } else {
      backToTopBtn.style.display = "none";
    }
  }

  // Modern splash animation is now handled by modern-splash.js
  // Neural chatbot functionality is now handled by futuristic-chatbot.js
</script>
</body>
</html>
