/**
 * Advanced AI Projects Effects
 * Modern, interactive effects for the AI projects section
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize all effects
  initProjectCardEffects();
  initParallaxEffects();
  initMouseTrackingEffects();
  initProjectAnimations();
});

/**
 * Initialize advanced project card effects
 */
function initProjectCardEffects() {
  const projectCards = document.querySelectorAll('.ai-project-card');
  
  projectCards.forEach((card, index) => {
    // Add staggered entrance animation
    card.style.animationDelay = `${index * 0.1}s`;
    
    // Add 3D tilt effect
    addTiltEffect(card);
    
    // Add particle effects on hover
    addParticleEffects(card);
    
    // Add glitch effect to project type badge
    addGlitchEffect(card);
  });
}

/**
 * Add 3D tilt effect to project cards
 */
function addTiltEffect(card) {
  card.addEventListener('mousemove', (e) => {
    const rect = card.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    
    const rotateX = (y - centerY) / 10;
    const rotateY = (centerX - x) / 10;
    
    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(20px)`;
  });
  
  card.addEventListener('mouseleave', () => {
    card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
  });
}

/**
 * Add particle effects on hover
 */
function addParticleEffects(card) {
  card.addEventListener('mouseenter', () => {
    createParticles(card, 15);
  });
}

/**
 * Create floating particles
 */
function createParticles(container, count) {
  for (let i = 0; i < count; i++) {
    const particle = document.createElement('div');
    particle.className = 'floating-particle';
    
    // Random position
    const x = Math.random() * container.offsetWidth;
    const y = Math.random() * container.offsetHeight;
    
    // Random size and color
    const size = Math.random() * 4 + 2;
    const opacity = Math.random() * 0.5 + 0.3;
    
    particle.style.cssText = `
      position: absolute;
      left: ${x}px;
      top: ${y}px;
      width: ${size}px;
      height: ${size}px;
      background: rgba(180, 44, 29, ${opacity});
      border-radius: 50%;
      pointer-events: none;
      z-index: 10;
      animation: floatUp 2s ease-out forwards;
    `;
    
    container.appendChild(particle);
    
    // Remove particle after animation
    setTimeout(() => {
      particle.remove();
    }, 2000);
  }
}

/**
 * Add glitch effect to project type badges
 */
function addGlitchEffect(card) {
  const badge = card.querySelector('.project-type');
  if (!badge) return;
  
  badge.addEventListener('mouseenter', () => {
    badge.style.animation = 'glitchEffect 0.3s ease-in-out';
  });
  
  badge.addEventListener('animationend', () => {
    badge.style.animation = '';
  });
}

/**
 * Initialize parallax effects for background elements
 */
function initParallaxEffects() {
  const projectsSection = document.querySelector('.ai-projects');
  if (!projectsSection) return;
  
  window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const parallax = projectsSection.querySelector('.neural-network-bg');
    
    if (parallax) {
      const speed = scrolled * 0.5;
      parallax.style.transform = `translateY(${speed}px)`;
    }
  });
}

/**
 * Initialize mouse tracking effects
 */
function initMouseTrackingEffects() {
  const projectsSection = document.querySelector('.ai-projects');
  if (!projectsSection) return;
  
  projectsSection.addEventListener('mousemove', (e) => {
    const x = e.clientX / window.innerWidth;
    const y = e.clientY / window.innerHeight;
    
    // Update CSS custom properties for mouse position
    projectsSection.style.setProperty('--mouse-x', x);
    projectsSection.style.setProperty('--mouse-y', y);
  });
}

/**
 * Initialize project animations
 */
function initProjectAnimations() {
  // Create intersection observer for scroll animations
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in');
        
        // Add staggered animation to child elements
        const techTags = entry.target.querySelectorAll('.tech-tag');
        techTags.forEach((tag, index) => {
          setTimeout(() => {
            tag.style.animation = 'slideInUp 0.6s ease-out forwards';
          }, index * 100);
        });
      }
    });
  }, {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  });
  
  // Observe all project cards
  const projectCards = document.querySelectorAll('.ai-project-card');
  projectCards.forEach(card => {
    observer.observe(card);
  });
}

/**
 * Add dynamic CSS animations
 */
const style = document.createElement('style');
style.textContent = `
  @keyframes floatUp {
    0% {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
    100% {
      transform: translateY(-100px) scale(0);
      opacity: 0;
    }
  }
  
  @keyframes glitchEffect {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
  }
  
  @keyframes slideInUp {
    0% {
      transform: translateY(30px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  .ai-project-card.animate-in {
    animation: cardEntrance 0.8s cubic-bezier(0.23, 1, 0.32, 1) forwards;
  }
  
  @keyframes cardEntrance {
    0% {
      transform: translateY(50px) scale(0.9);
      opacity: 0;
    }
    100% {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }
  
  /* Mouse tracking gradient effect */
  .ai-projects::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
      600px circle at calc(var(--mouse-x, 0.5) * 100%) calc(var(--mouse-y, 0.5) * 100%),
      rgba(180, 44, 29, 0.05) 0%,
      transparent 40%
    );
    pointer-events: none;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .ai-projects:hover::after {
    opacity: 1;
  }
`;

document.head.appendChild(style);
