/* AI Projects Section - Ultra-Modern, Advanced UI for AI Student Portfolio */

/* Main Container with Holographic Background */
.ai-projects {
  position: relative;
  background: linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%);
  padding: 120px 0;
  color: #ffffff;
  overflow: hidden;
  min-height: 100vh;
}

/* Advanced Neural Network Background with Particles */
.neural-network-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.15;
  pointer-events: none;
  z-index: 0;
}

/* Floating Particles Background */
.ai-projects::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(180, 44, 29, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(180, 44, 29, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(180, 44, 29, 0.05) 0%, transparent 50%);
  animation: particleFloat 20s ease-in-out infinite;
  z-index: 0;
}

@keyframes particleFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Ultra-Modern Section Header */
.ai-projects .section-header {
  position: relative;
  margin-bottom: 80px;
  z-index: 1;
  text-align: center;
}

.ai-projects .section-title {
  position: relative;
  display: inline-block;
  font-size: 4rem;
  font-weight: 900;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #ffffff 0%, #b42c1d 50%, #ffffff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-transform: uppercase;
  letter-spacing: 4px;
  text-shadow: 0 0 30px rgba(180, 44, 29, 0.5);
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% { filter: brightness(1) drop-shadow(0 0 10px rgba(180, 44, 29, 0.3)); }
  100% { filter: brightness(1.2) drop-shadow(0 0 20px rgba(180, 44, 29, 0.6)); }
}

.ai-projects .section-title::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(45deg, transparent, rgba(180, 44, 29, 0.1), transparent);
  border-radius: 10px;
  z-index: -1;
  animation: titleBorder 4s linear infinite;
}

@keyframes titleBorder {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.ai-projects .section-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #b42c1d, #ff7e5f);
  border-radius: 2px;
}

.ai-projects .section-subtitle {
  max-width: 700px;
  margin: 20px auto 0;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

/* Ultra-Modern Project Cards Container */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  position: relative;
  z-index: 1;
  perspective: 1000px;
  padding: 20px;
}

/* Futuristic AI Project Card */
.ai-project-card {
  position: relative;
  background: linear-gradient(145deg, rgba(15, 15, 15, 0.95) 0%, rgba(25, 25, 25, 0.9) 100%);
  border-radius: 20px;
  overflow: hidden;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(180, 44, 29, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  height: 450px;
  display: flex;
  flex-direction: column;
  transform-style: preserve-3d;
  backdrop-filter: blur(10px);
  border: 2px solid transparent;
  background-clip: padding-box;
}

.ai-project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(180, 44, 29, 0.1) 0%,
    transparent 25%,
    transparent 75%,
    rgba(180, 44, 29, 0.1) 100%);
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.6s ease;
  pointer-events: none;
  z-index: 1;
}

.ai-project-card:hover {
  transform: translateY(-20px) rotateX(5deg) rotateY(5deg);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.7),
    0 0 0 1px rgba(180, 44, 29, 0.5),
    0 0 30px rgba(180, 44, 29, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(180, 44, 29, 0.6);
}

.ai-project-card:hover::before {
  opacity: 1;
}

/* Advanced Project Image Container */
.project-image-container {
  position: relative;
  height: 220px;
  overflow: hidden;
  border-radius: 15px 15px 0 0;
  background: linear-gradient(45deg, rgba(180, 44, 29, 0.1), rgba(0, 0, 0, 0.3));
}

.project-image-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(180, 44, 29, 0.2) 0%,
    transparent 30%,
    transparent 70%,
    rgba(180, 44, 29, 0.2) 100%);
  opacity: 0;
  transition: opacity 0.6s ease;
  z-index: 2;
  pointer-events: none;
}

.project-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
  filter: brightness(0.8) contrast(1.1);
}

.ai-project-card:hover .project-image-container::before {
  opacity: 1;
}

.ai-project-card:hover .project-image {
  transform: scale(1.15) rotate(2deg);
  filter: brightness(1) contrast(1.2);
}

/* Futuristic Project Type Badge */
.project-type {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.9) 0%, rgba(255, 126, 95, 0.8) 100%);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  z-index: 3;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 4px 15px rgba(180, 44, 29, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Advanced Project Content */
.project-content {
  padding: 30px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.project-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(180, 44, 29, 0.5) 50%,
    transparent 100%);
  opacity: 0;
  transition: opacity 0.6s ease;
}

.ai-project-card:hover .project-content::before {
  opacity: 1;
}

.project-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 15px;
  line-height: 1.3;
  background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 10px rgba(180, 44, 29, 0.3);
}

.project-description {
  color: rgba(255, 255, 255, 0.85);
  font-size: 1rem;
  line-height: 1.7;
  margin-bottom: 25px;
  flex-grow: 1;
  font-weight: 400;
}

/* Futuristic Tech Stack Tags */
.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 25px;
}

.tech-tag {
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.3) 0%, rgba(180, 44, 29, 0.1) 100%);
  color: rgba(255, 255, 255, 0.95);
  padding: 8px 14px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid rgba(180, 44, 29, 0.4);
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.tech-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.tech-tag:hover {
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.5) 0%, rgba(180, 44, 29, 0.3) 100%);
  border-color: rgba(180, 44, 29, 0.7);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 5px 15px rgba(180, 44, 29, 0.3);
}

.tech-tag:hover::before {
  left: 100%;
}

/* Advanced Project Links */
.project-links {
  display: flex;
  gap: 12px;
  margin-top: auto;
}

.github-link {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: linear-gradient(135deg, rgba(180, 44, 29, 0.8) 0%, rgba(255, 126, 95, 0.7) 100%);
  color: #ffffff;
  padding: 12px 20px;
  border-radius: 30px;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 700;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  border: 2px solid rgba(180, 44, 29, 0.6);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.github-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.github-link:hover {
  background: linear-gradient(135deg, rgba(180, 44, 29, 1) 0%, rgba(255, 126, 95, 0.9) 100%);
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 10px 25px rgba(180, 44, 29, 0.5);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.3);
}

.github-link:hover::before {
  left: 100%;
}

.github-link i {
  font-size: 1.1rem;
}

/* AI Animation Effects */
.ai-glow {
  position: absolute;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(180, 44, 29, 0.4) 0%, rgba(180, 44, 29, 0) 70%);
  filter: blur(20px);
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.ai-project-card:hover .ai-glow {
  opacity: 1;
}

.ai-glow.top-left {
  top: -50px;
  left: -50px;
}

.ai-glow.bottom-right {
  bottom: -50px;
  right: -50px;
}

/* Neural Network Lines */
.neural-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
  background-image: 
    linear-gradient(90deg, rgba(180, 44, 29, 0.1) 1px, transparent 1px),
    linear-gradient(0deg, rgba(180, 44, 29, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.ai-project-card:hover .neural-lines {
  opacity: 0.4;
}

/* Featured Project */
.featured-project {
  grid-column: span 2;
  height: auto;
}

.featured-project .project-image-container {
  height: 300px;
}

.featured-project .project-content {
  padding: 30px;
}

.featured-project .project-title {
  font-size: 1.8rem;
}

.featured-project .project-description {
  font-size: 1.1rem;
}

.featured-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  background: linear-gradient(90deg, #b42c1d, #ff7e5f);
  color: white;
  padding: 6px 15px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 2;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  gap: 5px;
}

.featured-badge i {
  font-size: 0.9rem;
}

/* Mobile Projects Carousel */
.mobile-projects-carousel {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding: 20px 0;
  display: none;
}

.carousel-container {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.215, 0.610, 0.355, 1.000);
}

.carousel-slide {
  flex: 0 0 90%;
  max-width: 350px;
  margin: 0 15px;
}

.carousel-controls {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  gap: 20px;
}

.carousel-btn {
  background: rgba(20, 20, 20, 0.8);
  color: #ffffff;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.carousel-btn:hover {
  background: rgba(180, 44, 29, 0.8);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(180, 44, 29, 0.3);
}

.carousel-dots {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 8px;
}

.carousel-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-dot.active {
  background: #b42c1d;
  transform: scale(1.2);
}

/* View All Projects Button */
.view-all-projects {
  display: flex;
  justify-content: center;
  margin-top: 50px;
}

.view-all-btn {
  background: rgba(20, 20, 20, 0.8);
  color: #ffffff;
  border: 2px solid rgba(180, 44, 29, 0.5);
  border-radius: 30px;
  padding: 12px 30px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.view-all-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: linear-gradient(90deg, #b42c1d, #ff7e5f);
  z-index: -1;
  transition: width 0.3s ease;
}

.view-all-btn:hover::before {
  width: 100%;
}

.view-all-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(180, 44, 29, 0.3);
  border-color: transparent;
}

/* Advanced Responsive Design for Modern UI */
@media (max-width: 1200px) {
  .projects-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
  }

  .ai-projects .section-title {
    font-size: 3.5rem;
  }
}

@media (max-width: 992px) {
  .ai-projects .section-title {
    font-size: 3rem;
  }

  .projects-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 25px;
  }

  .ai-project-card {
    height: 420px;
  }
}

@media (max-width: 768px) {
  .ai-projects {
    padding: 80px 0;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 25px;
    padding: 10px;
  }

  .ai-project-card {
    height: auto;
    min-height: 400px;
    transform: none !important;
  }

  .ai-project-card:hover {
    transform: translateY(-10px) !important;
  }

  .project-image-container {
    height: 200px;
  }

  .project-content {
    padding: 25px;
  }

  .ai-projects .section-title {
    font-size: 2.5rem;
    letter-spacing: 2px;
  }

  .project-title {
    font-size: 1.3rem;
  }

  .project-description {
    font-size: 0.95rem;
    line-height: 1.6;
  }

  .tech-stack {
    gap: 8px;
  }

  .tech-tag {
    padding: 6px 12px;
    font-size: 0.75rem;
  }

  .github-link {
    padding: 10px 16px;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .ai-projects {
    padding: 60px 0;
  }

  .ai-projects .section-title {
    font-size: 2rem;
    letter-spacing: 1px;
  }

  .project-content {
    padding: 20px;
  }

  .project-title {
    font-size: 1.2rem;
  }

  .project-description {
    font-size: 0.9rem;
  }

  .tech-tag {
    padding: 5px 10px;
    font-size: 0.7rem;
  }

  .github-link {
    padding: 8px 14px;
    font-size: 0.8rem;
  }
}
